-- =====================================================
-- VOD PLATFORM PRODUCTION DATABASE SCHEMA
-- =====================================================
-- Version: 1.0
-- Date: 2025-07-17
-- Description: Production-ready schema combining original design with senior architect optimizations
-- Target: Scalable from startup to millions of users
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- High-performance ULID function (timestamp + randomness)
CREATE OR REPLACE FUNCTION generate_ulid() RETURNS TEXT AS $$
DECLARE
    timestamp_ms BIGINT;
    random_bytes BYTEA;
    ulid_chars TEXT := '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
    result TEXT := '';
    i INTEGER;
    value BIGINT;
BEGIN
    -- Get timestamp in milliseconds
    timestamp_ms := EXTRACT(EPOCH FROM NOW()) * 1000;
    
    -- Encode timestamp (10 chars)
    value := timestamp_ms;
    FOR i IN 1..10 LOOP
        result := substr(ulid_chars, (value % 32) + 1, 1) || result;
        value := value / 32;
    END LOOP;
    
    -- Add randomness (16 chars)
    random_bytes := gen_random_bytes(10);
    FOR i IN 0..9 LOOP
        value := get_byte(random_bytes, i);
        result := result || substr(ulid_chars, (value % 32) + 1, 1);
        IF i < 9 THEN
            result := result || substr(ulid_chars, ((value / 32) % 32) + 1, 1);
        END IF;
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Update timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- METADATA TABLES (Reference Data)
-- =====================================================

-- Genres
CREATE TABLE genres (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    parent_id TEXT REFERENCES genres(id),
    icon_url VARCHAR(500),
    color_hex VARCHAR(7),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_genres_active ON genres(is_active, sort_order) WHERE is_active = TRUE;
CREATE INDEX idx_genres_parent ON genres(parent_id) WHERE parent_id IS NOT NULL;

-- Regions (Countries/Areas)
CREATE TABLE regions (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(3) UNIQUE,
    region_type VARCHAR(20) DEFAULT 'country',
    parent_id TEXT REFERENCES regions(id),
    flag_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_regions_active ON regions(is_active, name);
CREATE INDEX idx_regions_code ON regions(code) WHERE code IS NOT NULL;

-- Languages
CREATE TABLE languages (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    native_name VARCHAR(100),
    is_subtitle_available BOOLEAN DEFAULT FALSE,
    is_audio_available BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_languages_code ON languages(code);
CREATE INDEX idx_languages_active ON languages(is_active);

-- Content Ratings
CREATE TABLE content_ratings (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    name VARCHAR(10) UNIQUE NOT NULL,
    description TEXT,
    min_age INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content Creators (Actors/Directors)
CREATE TABLE content_creators (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    external_id VARCHAR(50) UNIQUE,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    bio TEXT,
    date_of_birth DATE,
    place_of_birth VARCHAR(200),
    nationality VARCHAR(100),
    gender VARCHAR(10),
    profile_picture_url VARCHAR(1000),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by TEXT,
    version INTEGER DEFAULT 1,
    -- Optimized search vector
    search_vector TSVECTOR GENERATED ALWAYS AS (
        setweight(to_tsvector('english', name), 'A') ||
        setweight(to_tsvector('english', COALESCE(bio, '')), 'B')
    ) STORED
);

CREATE INDEX idx_content_creators_name ON content_creators(name) WHERE deleted_at IS NULL;
CREATE INDEX idx_content_creators_search ON content_creators USING GIN(search_vector) WHERE deleted_at IS NULL;
CREATE INDEX idx_content_creators_external ON content_creators(external_id) WHERE external_id IS NOT NULL;

-- Content Tags (Hashtags/Tags for content)
CREATE TABLE content_tags (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    color_hex VARCHAR(7),
    usage_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_content_tags_name ON content_tags(name) WHERE is_active = TRUE;
CREATE INDEX idx_content_tags_slug ON content_tags(slug) WHERE is_active = TRUE;
CREATE INDEX idx_content_tags_usage ON content_tags(usage_count DESC) WHERE is_active = TRUE;

CREATE TRIGGER content_tags_updated_at BEFORE UPDATE ON content_tags
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- CORE CONTENT TABLES
-- =====================================================

-- Movies (Core table with frequently accessed data)
CREATE TABLE movies (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    title VARCHAR(500) NOT NULL,
    original_title VARCHAR(500),
    slug VARCHAR(500) UNIQUE NOT NULL,
    synopsis TEXT,
    duration_minutes INTEGER NOT NULL,
    our_rating DECIMAL(3,1) CHECK (our_rating >= 0 AND our_rating <= 10),
    view_count BIGINT DEFAULT 0,
    favorite_count INTEGER DEFAULT 0,
    rating_count INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    is_premium BOOLEAN DEFAULT FALSE,
    is_adult BOOLEAN DEFAULT FALSE,
    content_rating_id TEXT REFERENCES content_ratings(id),
    release_date DATE,
    release_year INTEGER GENERATED ALWAYS AS (EXTRACT(YEAR FROM release_date)) STORED,
    poster_url VARCHAR(1000),
    backdrop_url VARCHAR(1000),
    quality_available TEXT[] DEFAULT ARRAY['HD'],
    keywords TEXT[], -- Keywords for search optimization
    status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'published', 'archived'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by TEXT,
    version INTEGER DEFAULT 1,
    -- Multi-language search vectors (including keywords)
    search_vector_en TSVECTOR GENERATED ALWAYS AS (
        setweight(to_tsvector('english', title), 'A') ||
        setweight(to_tsvector('english', COALESCE(original_title, '')), 'B') ||
        setweight(to_tsvector('english', COALESCE(synopsis, '')), 'C') ||
        setweight(to_tsvector('english', COALESCE(array_to_string(keywords, ' '), '')), 'D')
    ) STORED,
    search_vector_simple TSVECTOR GENERATED ALWAYS AS (
        setweight(to_tsvector('simple', title), 'A') ||
        setweight(to_tsvector('simple', COALESCE(original_title, '')), 'B') ||
        setweight(to_tsvector('simple', COALESCE(synopsis, '')), 'C') ||
        setweight(to_tsvector('simple', COALESCE(array_to_string(keywords, ' '), '')), 'D')
    ) STORED,
    -- Autocomplete support
    title_trigrams TEXT GENERATED ALWAYS AS (
        lower(title || ' ' || COALESCE(original_title, ''))
    ) STORED
);

-- Optimized indexes for movies
CREATE INDEX idx_movies_status ON movies(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_movies_featured ON movies(is_featured, our_rating DESC) WHERE is_featured = TRUE AND status = 'published' AND deleted_at IS NULL;
CREATE INDEX idx_movies_trending ON movies(is_trending, our_rating DESC, view_count DESC) WHERE status = 'published' AND is_trending = TRUE AND deleted_at IS NULL;
CREATE INDEX idx_movies_year ON movies(release_year DESC, our_rating DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_movies_rating ON movies(our_rating DESC, view_count DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_movies_popularity ON movies(view_count DESC, favorite_count DESC, rating_count DESC) WHERE status = 'published' AND deleted_at IS NULL;
CREATE INDEX idx_movies_search_en ON movies USING GIN(search_vector_en) WHERE deleted_at IS NULL;
CREATE INDEX idx_movies_search_simple ON movies USING GIN(search_vector_simple) WHERE deleted_at IS NULL;
CREATE INDEX idx_movies_trigrams ON movies USING GIN(title_trigrams gin_trgm_ops) WHERE deleted_at IS NULL;

-- Update trigger for movies
CREATE TRIGGER movies_updated_at BEFORE UPDATE ON movies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- TV Series (Core table)
CREATE TABLE tv_series (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    title VARCHAR(500) NOT NULL,
    original_title VARCHAR(500),
    slug VARCHAR(500) UNIQUE NOT NULL,
    synopsis TEXT,
    episode_duration_avg INTEGER,
    our_rating DECIMAL(3,1) CHECK (our_rating >= 0 AND our_rating <= 10),
    view_count BIGINT DEFAULT 0,
    favorite_count INTEGER DEFAULT 0,
    rating_count INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    is_premium BOOLEAN DEFAULT FALSE,
    is_adult BOOLEAN DEFAULT FALSE,
    content_rating_id TEXT REFERENCES content_ratings(id),
    release_date DATE,
    release_year INTEGER GENERATED ALWAYS AS (EXTRACT(YEAR FROM release_date)) STORED,
    total_episodes INTEGER DEFAULT 0,
    total_seasons INTEGER DEFAULT 0,
    poster_url VARCHAR(1000),
    backdrop_url VARCHAR(1000),
    keywords TEXT[], -- Keywords for search optimization
    series_status VARCHAR(20) DEFAULT 'ongoing', -- 'ongoing', 'completed', 'cancelled'
    status VARCHAR(20) DEFAULT 'draft',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by TEXT,
    version INTEGER DEFAULT 1,
    -- Search vectors (including keywords)
    search_vector_en TSVECTOR GENERATED ALWAYS AS (
        setweight(to_tsvector('english', title), 'A') ||
        setweight(to_tsvector('english', COALESCE(original_title, '')), 'B') ||
        setweight(to_tsvector('english', COALESCE(synopsis, '')), 'C') ||
        setweight(to_tsvector('english', COALESCE(array_to_string(keywords, ' '), '')), 'D')
    ) STORED,
    search_vector_simple TSVECTOR GENERATED ALWAYS AS (
        setweight(to_tsvector('simple', title), 'A') ||
        setweight(to_tsvector('simple', COALESCE(original_title, '')), 'B') ||
        setweight(to_tsvector('simple', COALESCE(synopsis, '')), 'C') ||
        setweight(to_tsvector('simple', COALESCE(array_to_string(keywords, ' '), '')), 'D')
    ) STORED,
    title_trigrams TEXT GENERATED ALWAYS AS (
        lower(title || ' ' || COALESCE(original_title, ''))
    ) STORED
);

-- Indexes for TV series (similar to movies)
CREATE INDEX idx_tv_series_status ON tv_series(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_tv_series_featured ON tv_series(is_featured, our_rating DESC) WHERE is_featured = TRUE AND status = 'published' AND deleted_at IS NULL;
CREATE INDEX idx_tv_series_trending ON tv_series(is_trending, our_rating DESC, view_count DESC) WHERE status = 'published' AND is_trending = TRUE AND deleted_at IS NULL;
CREATE INDEX idx_tv_series_ongoing ON tv_series(series_status, updated_at DESC) WHERE series_status = 'ongoing' AND deleted_at IS NULL;
CREATE INDEX idx_tv_series_episodes ON tv_series(total_episodes DESC, total_seasons DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_tv_series_search_en ON tv_series USING GIN(search_vector_en) WHERE deleted_at IS NULL;
CREATE INDEX idx_tv_series_search_simple ON tv_series USING GIN(search_vector_simple) WHERE deleted_at IS NULL;
CREATE INDEX idx_tv_series_trigrams ON tv_series USING GIN(title_trigrams gin_trgm_ops) WHERE deleted_at IS NULL;

CREATE TRIGGER tv_series_updated_at BEFORE UPDATE ON tv_series
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Seasons
CREATE TABLE seasons (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    tv_series_id TEXT NOT NULL REFERENCES tv_series(id) ON DELETE CASCADE,
    external_id VARCHAR(30),
    season_number INTEGER NOT NULL,
    name VARCHAR(200) NOT NULL,
    overview TEXT,
    episode_count INTEGER DEFAULT 0,
    air_date DATE,
    end_date DATE,
    poster_url VARCHAR(1000),
    sort_order INTEGER DEFAULT 0,
    is_special BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by TEXT,
    version INTEGER DEFAULT 1,
    UNIQUE(tv_series_id, season_number)
);

CREATE INDEX idx_seasons_series ON seasons(tv_series_id, sort_order) WHERE deleted_at IS NULL;
CREATE INDEX idx_seasons_external ON seasons(external_id) WHERE deleted_at IS NULL;

CREATE TRIGGER seasons_updated_at BEFORE UPDATE ON seasons
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Episodes
CREATE TABLE episodes (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    season_id TEXT NOT NULL REFERENCES seasons(id) ON DELETE CASCADE,
    external_id VARCHAR(30),
    episode_number INTEGER NOT NULL,
    title VARCHAR(300) NOT NULL,
    overview TEXT,
    duration_minutes INTEGER,
    air_date DATE,
    still_url VARCHAR(1000),
    rating DECIMAL(3,1) CHECK (rating >= 0 AND rating <= 10),
    vote_count INTEGER DEFAULT 0,
    view_count BIGINT DEFAULT 0,
    is_finale BOOLEAN DEFAULT FALSE,
    is_special BOOLEAN DEFAULT FALSE,
    is_filler BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by TEXT,
    version INTEGER DEFAULT 1,
    UNIQUE(season_id, episode_number)
);

CREATE INDEX idx_episodes_season ON episodes(season_id, episode_number) WHERE deleted_at IS NULL;
CREATE INDEX idx_episodes_air_date ON episodes(air_date DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_episodes_views ON episodes(view_count DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_episodes_external ON episodes(external_id) WHERE deleted_at IS NULL;

CREATE TRIGGER episodes_updated_at BEFORE UPDATE ON episodes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- DETAIL CONTENT TABLES (Extended Information)
-- =====================================================

-- Movie Details (1:1 relationship with movies)
CREATE TABLE movie_details (
    movie_id TEXT PRIMARY KEY REFERENCES movies(id) ON DELETE CASCADE,
    external_id VARCHAR(30) UNIQUE,
    tmdb_id VARCHAR(50),
    imdb_id VARCHAR(20),
    description TEXT,
    budget BIGINT,
    box_office BIGINT,
    production_companies TEXT[],
    filming_locations TEXT[],
    tmdb_rating DECIMAL(3,1),
    imdb_rating DECIMAL(3,1),
    trailer_url VARCHAR(1000),
    logo_url VARCHAR(1000),
    keywords TEXT[],
    meta_title VARCHAR(200),
    meta_description VARCHAR(500),
    download_count BIGINT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_movie_details_tmdb_id ON movie_details(tmdb_id) WHERE tmdb_id IS NOT NULL;
CREATE INDEX idx_movie_details_imdb_id ON movie_details(imdb_id) WHERE imdb_id IS NOT NULL;
CREATE INDEX idx_movie_details_external_id ON movie_details(external_id) WHERE external_id IS NOT NULL;

CREATE TRIGGER movie_details_updated_at BEFORE UPDATE ON movie_details
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- TV Series Details (1:1 relationship with tv_series)
CREATE TABLE tv_series_details (
    tv_series_id TEXT PRIMARY KEY REFERENCES tv_series(id) ON DELETE CASCADE,
    external_id VARCHAR(30) UNIQUE,
    tmdb_id VARCHAR(50),
    description TEXT,
    air_schedule JSONB,
    next_episode_date DATE,
    studio VARCHAR(200),
    source_material VARCHAR(100),
    production_year INTEGER,
    tmdb_rating DECIMAL(3,1),
    vote_count INTEGER DEFAULT 0,
    end_date DATE,
    trailer_url VARCHAR(1000),
    logo_url VARCHAR(1000),
    keywords TEXT[],
    meta_title VARCHAR(200),
    meta_description VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_tv_series_details_tmdb_id ON tv_series_details(tmdb_id) WHERE tmdb_id IS NOT NULL;
CREATE INDEX idx_tv_series_details_external_id ON tv_series_details(external_id) WHERE external_id IS NOT NULL;

CREATE TRIGGER tv_series_details_updated_at BEFORE UPDATE ON tv_series_details
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- RELATIONSHIP TABLES
-- =====================================================

-- Movie-Genre Relationships
CREATE TABLE movie_genres (
    movie_id TEXT REFERENCES movies(id) ON DELETE CASCADE,
    genre_id TEXT REFERENCES genres(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (movie_id, genre_id)
);

CREATE INDEX idx_movie_genres_movie ON movie_genres(movie_id);
CREATE INDEX idx_movie_genres_genre ON movie_genres(genre_id);

-- TV Series-Genre Relationships
CREATE TABLE tv_series_genres (
    tv_series_id TEXT REFERENCES tv_series(id) ON DELETE CASCADE,
    genre_id TEXT REFERENCES genres(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (tv_series_id, genre_id)
);

CREATE INDEX idx_tv_series_genres_series ON tv_series_genres(tv_series_id);
CREATE INDEX idx_tv_series_genres_genre ON tv_series_genres(genre_id);

-- Movie-Region Relationships
CREATE TABLE movie_regions (
    movie_id TEXT REFERENCES movies(id) ON DELETE CASCADE,
    region_id TEXT REFERENCES regions(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (movie_id, region_id)
);

CREATE INDEX idx_movie_regions_movie ON movie_regions(movie_id);
CREATE INDEX idx_movie_regions_region ON movie_regions(region_id);

-- TV Series-Region Relationships
CREATE TABLE tv_series_regions (
    tv_series_id TEXT REFERENCES tv_series(id) ON DELETE CASCADE,
    region_id TEXT REFERENCES regions(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (tv_series_id, region_id)
);

CREATE INDEX idx_tv_series_regions_series ON tv_series_regions(tv_series_id);
CREATE INDEX idx_tv_series_regions_region ON tv_series_regions(region_id);

-- Movie-Creator Relationships
CREATE TABLE movie_creators (
    movie_id TEXT REFERENCES movies(id) ON DELETE CASCADE,
    creator_id TEXT REFERENCES content_creators(id) ON DELETE RESTRICT,
    role VARCHAR(100) NOT NULL, -- 'director', 'actor', 'producer', etc.
    character_name VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (movie_id, creator_id, role)
);

CREATE INDEX idx_movie_creators_movie ON movie_creators(movie_id, role);
CREATE INDEX idx_movie_creators_creator ON movie_creators(creator_id, role);

-- TV Series-Creator Relationships
CREATE TABLE tv_series_creators (
    tv_series_id TEXT REFERENCES tv_series(id) ON DELETE CASCADE,
    creator_id TEXT REFERENCES content_creators(id) ON DELETE RESTRICT,
    role VARCHAR(100) NOT NULL,
    character_name VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (tv_series_id, creator_id, role)
);

CREATE INDEX idx_tv_series_creators_series ON tv_series_creators(tv_series_id, role);
CREATE INDEX idx_tv_series_creators_creator ON tv_series_creators(creator_id, role);

-- Movie-Tag Relationships
CREATE TABLE movie_tags (
    movie_id TEXT REFERENCES movies(id) ON DELETE CASCADE,
    tag_id TEXT REFERENCES content_tags(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (movie_id, tag_id)
);

CREATE INDEX idx_movie_tags_movie ON movie_tags(movie_id);
CREATE INDEX idx_movie_tags_tag ON movie_tags(tag_id);

-- TV Series-Tag Relationships
CREATE TABLE tv_series_tags (
    tv_series_id TEXT REFERENCES tv_series(id) ON DELETE CASCADE,
    tag_id TEXT REFERENCES content_tags(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (tv_series_id, tag_id)
);

CREATE INDEX idx_tv_series_tags_series ON tv_series_tags(tv_series_id);
CREATE INDEX idx_tv_series_tags_tag ON tv_series_tags(tag_id);

-- Trigger to update tag usage count
CREATE OR REPLACE FUNCTION update_tag_usage_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE content_tags
        SET usage_count = usage_count + 1
        WHERE id = NEW.tag_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE content_tags
        SET usage_count = GREATEST(usage_count - 1, 0)
        WHERE id = OLD.tag_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for tag usage count
CREATE TRIGGER movie_tags_usage_count
    AFTER INSERT OR DELETE ON movie_tags
    FOR EACH ROW EXECUTE FUNCTION update_tag_usage_count();

CREATE TRIGGER tv_series_tags_usage_count
    AFTER INSERT OR DELETE ON tv_series_tags
    FOR EACH ROW EXECUTE FUNCTION update_tag_usage_count();

-- =====================================================
-- USER SYSTEM TABLES
-- =====================================================

-- Users (Core user data)
CREATE TABLE users (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20) UNIQUE,
    is_email_verified BOOLEAN DEFAULT FALSE,
    is_phone_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP WITH TIME ZONE,
    registration_ip INET,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by TEXT,
    version INTEGER DEFAULT 1
);

CREATE INDEX idx_users_email ON users(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_username ON users(username) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_active ON users(is_active) WHERE is_active = TRUE AND deleted_at IS NULL;
CREATE INDEX idx_users_last_login ON users(last_login DESC) WHERE deleted_at IS NULL;

CREATE TRIGGER users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- User Profiles (Extended user information)
CREATE TABLE user_profiles (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    display_name VARCHAR(100),
    avatar_url VARCHAR(1000),
    bio TEXT,
    date_of_birth DATE,
    gender VARCHAR(10),
    country_id TEXT REFERENCES regions(id),
    preferred_language_id TEXT REFERENCES languages(id),
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_user_profiles_user ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_country ON user_profiles(country_id) WHERE country_id IS NOT NULL;

CREATE TRIGGER user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- STREAMING INFRASTRUCTURE TABLES
-- =====================================================

-- Movie Stream Sources (Simplified)
CREATE TABLE movie_stream_sources (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    movie_id TEXT NOT NULL REFERENCES movies(id) ON DELETE CASCADE,
    provider_name VARCHAR(100) NOT NULL,
    url TEXT NOT NULL,
    stream_type VARCHAR(20) NOT NULL, -- 'mp4', 'hls', 'embed'
    quality VARCHAR(10) NOT NULL, -- 'SD', 'HD', 'FHD', '4K'
    is_active BOOLEAN DEFAULT TRUE,
    is_premium BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_movie_stream_sources_movie ON movie_stream_sources(movie_id, sort_order) WHERE deleted_at IS NULL;
CREATE INDEX idx_movie_stream_sources_quality ON movie_stream_sources(movie_id, quality, is_active) WHERE deleted_at IS NULL;
CREATE INDEX idx_movie_stream_sources_active ON movie_stream_sources(is_active, is_premium) WHERE is_active = TRUE AND deleted_at IS NULL;
CREATE INDEX idx_movie_stream_sources_primary ON movie_stream_sources(movie_id) WHERE is_primary = TRUE AND is_active = TRUE AND deleted_at IS NULL;
CREATE INDEX idx_movie_stream_sources_type ON movie_stream_sources(stream_type, quality) WHERE is_active = TRUE;

CREATE TRIGGER movie_stream_sources_updated_at BEFORE UPDATE ON movie_stream_sources
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Episode Stream Sources (Simplified)
CREATE TABLE episode_stream_sources (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    episode_id TEXT NOT NULL REFERENCES episodes(id) ON DELETE CASCADE,
    provider_name VARCHAR(100) NOT NULL,
    provider_type VARCHAR(50) DEFAULT 'external',
    url TEXT NOT NULL,
    stream_type VARCHAR(20) NOT NULL, -- 'mp4', 'hls', 'embed', etc.
    quality VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_premium BOOLEAN DEFAULT FALSE,
    requires_auth BOOLEAN DEFAULT FALSE,
    available_from TIMESTAMP WITH TIME ZONE,
    available_until TIMESTAMP WITH TIME ZONE,
    geo_restrictions TEXT[],
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    has_intro_skip BOOLEAN DEFAULT FALSE,
    intro_start_seconds INTEGER,
    intro_end_seconds INTEGER,
    has_credits_skip BOOLEAN DEFAULT FALSE,
    credits_start_seconds INTEGER,
    has_preview BOOLEAN DEFAULT FALSE,
    preview_end_seconds INTEGER,
    auto_next_episode BOOLEAN DEFAULT TRUE,
    next_episode_countdown_seconds INTEGER DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by TEXT,
    version INTEGER DEFAULT 1
);

CREATE INDEX idx_episode_stream_sources_episode ON episode_stream_sources(episode_id, sort_order) WHERE deleted_at IS NULL;
CREATE INDEX idx_episode_stream_sources_quality ON episode_stream_sources(episode_id, quality, is_active) WHERE deleted_at IS NULL;
CREATE INDEX idx_episode_stream_sources_active ON episode_stream_sources(is_active, is_premium) WHERE is_active = TRUE AND deleted_at IS NULL;
CREATE INDEX idx_episode_stream_sources_primary ON episode_stream_sources(episode_id) WHERE is_primary = TRUE AND is_active = TRUE AND deleted_at IS NULL;
CREATE INDEX idx_episode_stream_sources_type ON episode_stream_sources(stream_type, quality) WHERE is_active = TRUE;

CREATE TRIGGER episode_stream_sources_updated_at BEFORE UPDATE ON episode_stream_sources
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Movie Stream Subtitles
CREATE TABLE movie_stream_subtitles (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    movie_stream_source_id TEXT NOT NULL REFERENCES movie_stream_sources(id) ON DELETE CASCADE,
    language_id TEXT NOT NULL REFERENCES languages(id),
    url VARCHAR(1000) NOT NULL,
    label VARCHAR(100),
    kind VARCHAR(50), -- 'subtitles', 'captions', 'descriptions', 'forced'
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(movie_stream_source_id, language_id, kind)
);

CREATE INDEX idx_movie_stream_subtitles_source ON movie_stream_subtitles(movie_stream_source_id);
CREATE INDEX idx_movie_stream_subtitles_language ON movie_stream_subtitles(language_id);

-- Episode Stream Subtitles
CREATE TABLE episode_stream_subtitles (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    episode_stream_source_id TEXT NOT NULL REFERENCES episode_stream_sources(id) ON DELETE CASCADE,
    language_id TEXT NOT NULL REFERENCES languages(id),
    url VARCHAR(1000) NOT NULL,
    label VARCHAR(100),
    kind VARCHAR(50),
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(episode_stream_source_id, language_id, kind)
);

CREATE INDEX idx_episode_stream_subtitles_source ON episode_stream_subtitles(episode_stream_source_id);
CREATE INDEX idx_episode_stream_subtitles_language ON episode_stream_subtitles(language_id);

-- Movie Stream Audios
CREATE TABLE movie_stream_audios (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    movie_stream_source_id TEXT NOT NULL REFERENCES movie_stream_sources(id) ON DELETE CASCADE,
    language_id TEXT NOT NULL REFERENCES languages(id),
    label VARCHAR(100),
    codec VARCHAR(50),
    channels VARCHAR(20), -- '2.0', '5.1', '7.1'
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(movie_stream_source_id, language_id)
);

CREATE INDEX idx_movie_stream_audios_source ON movie_stream_audios(movie_stream_source_id);
CREATE INDEX idx_movie_stream_audios_language ON movie_stream_audios(language_id);

-- Episode Stream Audios
CREATE TABLE episode_stream_audios (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    episode_stream_source_id TEXT NOT NULL REFERENCES episode_stream_sources(id) ON DELETE CASCADE,
    language_id TEXT NOT NULL REFERENCES languages(id),
    label VARCHAR(100),
    codec VARCHAR(50),
    channels VARCHAR(20),
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(episode_stream_source_id, language_id)
);

CREATE INDEX idx_episode_stream_audios_source ON episode_stream_audios(episode_stream_source_id);
CREATE INDEX idx_episode_stream_audios_language ON episode_stream_audios(language_id);

-- =====================================================
-- USER INTERACTION TABLES (HASH PARTITIONED)
-- =====================================================

-- User Movie Watch History (Hash partitioned by user_id)
CREATE TABLE user_movie_watch_history (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL,
    movie_id TEXT NOT NULL REFERENCES movies(id) ON DELETE CASCADE,
    watched_duration_seconds INTEGER DEFAULT 0,
    total_duration_seconds INTEGER,
    progress_percentage DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE
            WHEN total_duration_seconds > 0 THEN
                ROUND((watched_duration_seconds::DECIMAL / total_duration_seconds) * 100, 2)
            ELSE 0
        END
    ) STORED,
    completed BOOLEAN GENERATED ALWAYS AS (progress_percentage >= 90) STORED,
    device_type VARCHAR(20), -- 'mobile', 'tablet', 'desktop', 'tv'
    stream_quality VARCHAR(10),
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_watched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    device_info JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, movie_id)
) PARTITION BY HASH (user_id);

-- Create 16 partitions for even distribution
DO $$
BEGIN
    FOR i IN 0..15 LOOP
        EXECUTE format('
            CREATE TABLE user_movie_watch_history_%s
            PARTITION OF user_movie_watch_history
            FOR VALUES WITH (modulus 16, remainder %s)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_movie_watch_history_%s_user_recent
            ON user_movie_watch_history_%s(user_id, last_watched_at DESC)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_movie_watch_history_%s_movie
            ON user_movie_watch_history_%s(movie_id, last_watched_at DESC)', i, i);
    END LOOP;
END $$;

-- User Episode Watch History (Hash partitioned by user_id)
CREATE TABLE user_episode_watch_history (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL,
    episode_id TEXT NOT NULL REFERENCES episodes(id) ON DELETE CASCADE,
    watched_duration_seconds INTEGER DEFAULT 0,
    total_duration_seconds INTEGER,
    progress_percentage DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE
            WHEN total_duration_seconds > 0 THEN
                ROUND((watched_duration_seconds::DECIMAL / total_duration_seconds) * 100, 2)
            ELSE 0
        END
    ) STORED,
    completed BOOLEAN GENERATED ALWAYS AS (progress_percentage >= 90) STORED,
    device_type VARCHAR(20),
    stream_quality VARCHAR(10),
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_watched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    device_info JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, episode_id)
) PARTITION BY HASH (user_id);

-- Create 16 partitions for episodes
DO $$
BEGIN
    FOR i IN 0..15 LOOP
        EXECUTE format('
            CREATE TABLE user_episode_watch_history_%s
            PARTITION OF user_episode_watch_history
            FOR VALUES WITH (modulus 16, remainder %s)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_episode_watch_history_%s_user_recent
            ON user_episode_watch_history_%s(user_id, last_watched_at DESC)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_episode_watch_history_%s_episode
            ON user_episode_watch_history_%s(episode_id, last_watched_at DESC)', i, i);
    END LOOP;
END $$;

-- User Movie Favorites (Hash partitioned by user_id)
CREATE TABLE user_movie_favorites (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL,
    movie_id TEXT NOT NULL REFERENCES movies(id) ON DELETE CASCADE,
    note TEXT,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, movie_id)
) PARTITION BY HASH (user_id);

-- Create 8 partitions for favorites
DO $$
BEGIN
    FOR i IN 0..7 LOOP
        EXECUTE format('
            CREATE TABLE user_movie_favorites_%s
            PARTITION OF user_movie_favorites
            FOR VALUES WITH (modulus 8, remainder %s)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_movie_favorites_%s_user
            ON user_movie_favorites_%s(user_id, created_at DESC)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_movie_favorites_%s_movie
            ON user_movie_favorites_%s(movie_id)', i, i);
    END LOOP;
END $$;

-- User Series Favorites (Hash partitioned by user_id)
CREATE TABLE user_series_favorites (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL,
    tv_series_id TEXT NOT NULL REFERENCES tv_series(id) ON DELETE CASCADE,
    note TEXT,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, tv_series_id)
) PARTITION BY HASH (user_id);

-- Create 8 partitions for series favorites
DO $$
BEGIN
    FOR i IN 0..7 LOOP
        EXECUTE format('
            CREATE TABLE user_series_favorites_%s
            PARTITION OF user_series_favorites
            FOR VALUES WITH (modulus 8, remainder %s)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_series_favorites_%s_user
            ON user_series_favorites_%s(user_id, created_at DESC)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_series_favorites_%s_series
            ON user_series_favorites_%s(tv_series_id)', i, i);
    END LOOP;
END $$;

-- User Movie Ratings (Hash partitioned by user_id)
CREATE TABLE user_movie_ratings (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL,
    movie_id TEXT NOT NULL REFERENCES movies(id) ON DELETE CASCADE,
    rating DECIMAL(3,1) CHECK (rating >= 0 AND rating <= 10) NOT NULL,
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, movie_id)
) PARTITION BY HASH (user_id);

-- Create 8 partitions for movie ratings
DO $$
BEGIN
    FOR i IN 0..7 LOOP
        EXECUTE format('
            CREATE TABLE user_movie_ratings_%s
            PARTITION OF user_movie_ratings
            FOR VALUES WITH (modulus 8, remainder %s)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_movie_ratings_%s_user
            ON user_movie_ratings_%s(user_id, created_at DESC)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_movie_ratings_%s_movie
            ON user_movie_ratings_%s(movie_id, rating DESC)', i, i);
    END LOOP;
END $$;

-- User Series Ratings (Hash partitioned by user_id)
CREATE TABLE user_series_ratings (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL,
    tv_series_id TEXT NOT NULL REFERENCES tv_series(id) ON DELETE CASCADE,
    rating DECIMAL(3,1) CHECK (rating >= 0 AND rating <= 10) NOT NULL,
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, tv_series_id)
) PARTITION BY HASH (user_id);

-- Create 8 partitions for series ratings
DO $$
BEGIN
    FOR i IN 0..7 LOOP
        EXECUTE format('
            CREATE TABLE user_series_ratings_%s
            PARTITION OF user_series_ratings
            FOR VALUES WITH (modulus 8, remainder %s)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_series_ratings_%s_user
            ON user_series_ratings_%s(user_id, created_at DESC)', i, i);

        EXECUTE format('
            CREATE INDEX idx_user_series_ratings_%s_series
            ON user_series_ratings_%s(tv_series_id, rating DESC)', i, i);
    END LOOP;
END $$;

-- =====================================================
-- BUSINESS TABLES (Subscriptions & Payments)
-- =====================================================

-- Subscriptions (Plans)
CREATE TABLE subscriptions (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD' NOT NULL,
    duration_days INTEGER NOT NULL,
    features TEXT[],
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_subscriptions_active ON subscriptions(is_active, price) WHERE is_active = TRUE;

CREATE TRIGGER subscriptions_updated_at BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- User Subscriptions
CREATE TABLE user_subscriptions (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subscription_id TEXT NOT NULL REFERENCES subscriptions(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL, -- 'active', 'expired', 'cancelled', 'pending'
    starts_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ends_at TIMESTAMP WITH TIME ZONE NOT NULL,
    auto_renew BOOLEAN DEFAULT TRUE,
    payment_method_id VARCHAR(100),
    last_billed_at TIMESTAMP WITH TIME ZONE,
    next_billing_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_user_subscriptions_user ON user_subscriptions(user_id, status);
CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(status, ends_at) WHERE status = 'active';
CREATE INDEX idx_user_subscriptions_billing ON user_subscriptions(next_billing_at) WHERE auto_renew = TRUE AND status = 'active';

CREATE TRIGGER user_subscriptions_updated_at BEFORE UPDATE ON user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Payments
CREATE TABLE payments (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_subscription_id TEXT REFERENCES user_subscriptions(id) ON DELETE SET NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD' NOT NULL,
    payment_gateway_id VARCHAR(100),
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(20) NOT NULL, -- 'pending', 'completed', 'failed', 'refunded'
    payment_method_type VARCHAR(50),
    paid_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_payments_user ON payments(user_id, paid_at DESC);
CREATE INDEX idx_payments_transaction ON payments(transaction_id);
CREATE INDEX idx_payments_status ON payments(status, paid_at DESC);
CREATE INDEX idx_payments_subscription ON payments(user_subscription_id) WHERE user_subscription_id IS NOT NULL;

-- User Recommendations
CREATE TABLE user_recommendations (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content_type VARCHAR(20) NOT NULL, -- 'movie', 'tv_series'
    content_id TEXT NOT NULL,
    recommendation_type VARCHAR(50) NOT NULL, -- 'collaborative', 'content_based', 'trending', 'similar'
    score DECIMAL(5,4) NOT NULL,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, content_id, content_type, recommendation_type)
);

CREATE INDEX idx_user_recommendations_user_score ON user_recommendations(user_id, score DESC, generated_at DESC);
CREATE INDEX idx_user_recommendations_content ON user_recommendations(content_id, content_type);
CREATE INDEX idx_user_recommendations_expiry ON user_recommendations(expires_at) WHERE expires_at > NOW();

-- =====================================================
-- STREAM METRICS TABLES
-- =====================================================

-- Movie Stream Metrics
CREATE TABLE movie_stream_metrics (
    movie_stream_source_id TEXT PRIMARY KEY REFERENCES movie_stream_sources(id) ON DELETE CASCADE,
    play_count BIGINT DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 100.00,
    avg_load_time_ms INTEGER,
    last_played TIMESTAMP WITH TIME ZONE,
    last_error TIMESTAMP WITH TIME ZONE,
    last_health_check TIMESTAMP WITH TIME ZONE,
    health_status VARCHAR(20) DEFAULT 'unknown', -- 'healthy', 'warning', 'error', 'offline'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_movie_stream_metrics_health ON movie_stream_metrics(health_status, last_health_check);
CREATE INDEX idx_movie_stream_metrics_play_count ON movie_stream_metrics(play_count DESC);

CREATE TRIGGER movie_stream_metrics_updated_at BEFORE UPDATE ON movie_stream_metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Episode Stream Metrics
CREATE TABLE episode_stream_metrics (
    episode_stream_source_id TEXT PRIMARY KEY REFERENCES episode_stream_sources(id) ON DELETE CASCADE,
    play_count BIGINT DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 100.00,
    avg_load_time_ms INTEGER,
    last_played TIMESTAMP WITH TIME ZONE,
    last_error TIMESTAMP WITH TIME ZONE,
    last_health_check TIMESTAMP WITH TIME ZONE,
    health_status VARCHAR(20) DEFAULT 'unknown',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_episode_stream_metrics_health ON episode_stream_metrics(health_status, last_health_check);
CREATE INDEX idx_episode_stream_metrics_play_count ON episode_stream_metrics(play_count DESC);

CREATE TRIGGER episode_stream_metrics_updated_at BEFORE UPDATE ON episode_stream_metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- MATERIALIZED VIEWS FOR READ SCALING
-- =====================================================

-- Movies with denormalized data for fast access
CREATE MATERIALIZED VIEW movies_denormalized AS
SELECT
    m.id,
    m.title,
    m.slug,
    m.synopsis,
    m.poster_url,
    m.backdrop_url,
    m.our_rating,
    m.view_count,
    m.favorite_count,
    m.is_featured,
    m.is_trending,
    m.is_premium,
    m.is_adult,
    m.release_year,
    m.duration_minutes,
    m.keywords,
    m.created_at,

    -- Aggregated genres (JSON for fast access)
    COALESCE(
        json_agg(DISTINCT jsonb_build_object(
            'id', g.id,
            'name', g.name,
            'slug', g.slug
        )) FILTER (WHERE g.id IS NOT NULL),
        '[]'::json
    ) as genres,

    -- Aggregated tags
    COALESCE(
        json_agg(DISTINCT jsonb_build_object(
            'id', ct.id,
            'name', ct.name,
            'slug', ct.slug,
            'color_hex', ct.color_hex
        )) FILTER (WHERE ct.id IS NOT NULL),
        '[]'::json
    ) as tags,

    -- Aggregated creators by role
    COALESCE(
        json_object_agg(
            mc.role,
            json_agg(DISTINCT jsonb_build_object(
                'id', cc.id,
                'name', cc.name,
                'slug', cc.slug
            ))
        ) FILTER (WHERE cc.id IS NOT NULL),
        '{}'::json
    ) as creators,

    -- Stream quality availability
    COALESCE(
        array_agg(DISTINCT mss.quality ORDER BY mss.quality)
        FILTER (WHERE mss.quality IS NOT NULL),
        ARRAY[]::VARCHAR[]
    ) as available_qualities,

    -- Stream types availability
    COALESCE(
        array_agg(DISTINCT mss.stream_type ORDER BY mss.stream_type)
        FILTER (WHERE mss.stream_type IS NOT NULL),
        ARRAY[]::VARCHAR[]
    ) as available_stream_types,

    -- Subtitle languages
    COALESCE(
        array_agg(DISTINCT l.code ORDER BY l.code)
        FILTER (WHERE l.code IS NOT NULL),
        ARRAY[]::VARCHAR[]
    ) as subtitle_languages,

    -- Performance metrics
    COUNT(DISTINCT mss.id) as stream_source_count,
    AVG(mssm.success_rate) as avg_success_rate

FROM movies m
LEFT JOIN movie_genres mg ON m.id = mg.movie_id
LEFT JOIN genres g ON mg.genre_id = g.id AND g.is_active = TRUE
LEFT JOIN movie_tags mt ON m.id = mt.movie_id
LEFT JOIN content_tags ct ON mt.tag_id = ct.id AND ct.is_active = TRUE
LEFT JOIN movie_creators mc ON m.id = mc.movie_id
LEFT JOIN content_creators cc ON mc.creator_id = cc.id AND cc.deleted_at IS NULL
LEFT JOIN movie_stream_sources mss ON m.id = mss.movie_id AND mss.is_active = TRUE AND mss.deleted_at IS NULL
LEFT JOIN movie_stream_subtitles msub ON mss.id = msub.movie_stream_source_id AND msub.is_active = TRUE
LEFT JOIN languages l ON msub.language_id = l.id AND l.is_active = TRUE
LEFT JOIN movie_stream_metrics mssm ON mss.id = mssm.movie_stream_source_id
WHERE m.deleted_at IS NULL
GROUP BY m.id, m.title, m.slug, m.synopsis, m.poster_url, m.backdrop_url,
         m.our_rating, m.view_count, m.favorite_count, m.is_featured,
         m.is_trending, m.is_premium, m.is_adult, m.release_year, m.duration_minutes, m.keywords, m.created_at;

-- Optimized indexes for materialized view
CREATE UNIQUE INDEX idx_movies_denormalized_id ON movies_denormalized(id);
CREATE INDEX idx_movies_denormalized_featured ON movies_denormalized(is_featured, our_rating DESC) WHERE is_featured = TRUE;
CREATE INDEX idx_movies_denormalized_trending ON movies_denormalized(is_trending, view_count DESC) WHERE is_trending = TRUE;
CREATE INDEX idx_movies_denormalized_rating ON movies_denormalized(our_rating DESC, view_count DESC);
CREATE INDEX idx_movies_denormalized_year ON movies_denormalized(release_year DESC, our_rating DESC);

-- TV Series with denormalized data
CREATE MATERIALIZED VIEW tv_series_denormalized AS
SELECT
    ts.id,
    ts.title,
    ts.slug,
    ts.synopsis,
    ts.poster_url,
    ts.backdrop_url,
    ts.our_rating,
    ts.view_count,
    ts.favorite_count,
    ts.is_featured,
    ts.is_trending,
    ts.is_premium,
    ts.is_adult,
    ts.release_year,
    ts.total_episodes,
    ts.total_seasons,
    ts.series_status,
    ts.episode_duration_avg,
    ts.keywords,
    ts.created_at,

    -- Aggregated genres
    COALESCE(
        json_agg(DISTINCT jsonb_build_object(
            'id', g.id,
            'name', g.name,
            'slug', g.slug
        )) FILTER (WHERE g.id IS NOT NULL),
        '[]'::json
    ) as genres,

    -- Aggregated tags
    COALESCE(
        json_agg(DISTINCT jsonb_build_object(
            'id', ct.id,
            'name', ct.name,
            'slug', ct.slug,
            'color_hex', ct.color_hex
        )) FILTER (WHERE ct.id IS NOT NULL),
        '[]'::json
    ) as tags,

    -- Aggregated creators by role
    COALESCE(
        json_object_agg(
            tsc.role,
            json_agg(DISTINCT jsonb_build_object(
                'id', cc.id,
                'name', cc.name,
                'slug', cc.slug
            ))
        ) FILTER (WHERE cc.id IS NOT NULL),
        '{}'::json
    ) as creators,

    -- Seasons basic info
    COALESCE(
        json_agg(DISTINCT jsonb_build_object(
            'id', s.id,
            'season_number', s.season_number,
            'name', s.name,
            'episode_count', s.episode_count
        ) ORDER BY s.season_number) FILTER (WHERE s.id IS NOT NULL),
        '[]'::json
    ) as seasons

FROM tv_series ts
LEFT JOIN tv_series_genres tsg ON ts.id = tsg.tv_series_id
LEFT JOIN genres g ON tsg.genre_id = g.id AND g.is_active = TRUE
LEFT JOIN tv_series_tags tst ON ts.id = tst.tv_series_id
LEFT JOIN content_tags ct ON tst.tag_id = ct.id AND ct.is_active = TRUE
LEFT JOIN tv_series_creators tsc ON ts.id = tsc.tv_series_id
LEFT JOIN content_creators cc ON tsc.creator_id = cc.id AND cc.deleted_at IS NULL
LEFT JOIN seasons s ON ts.id = s.tv_series_id AND s.deleted_at IS NULL
WHERE ts.deleted_at IS NULL
GROUP BY ts.id, ts.title, ts.slug, ts.synopsis, ts.poster_url, ts.backdrop_url,
         ts.our_rating, ts.view_count, ts.favorite_count, ts.is_featured,
         ts.is_trending, ts.is_premium, ts.is_adult, ts.release_year,
         ts.total_episodes, ts.total_seasons, ts.series_status, ts.episode_duration_avg, ts.keywords, ts.created_at;

-- Optimized indexes for TV series materialized view
CREATE UNIQUE INDEX idx_tv_series_denormalized_id ON tv_series_denormalized(id);
CREATE INDEX idx_tv_series_denormalized_featured ON tv_series_denormalized(is_featured, our_rating DESC) WHERE is_featured = TRUE;
CREATE INDEX idx_tv_series_denormalized_trending ON tv_series_denormalized(is_trending, view_count DESC) WHERE is_trending = TRUE;
CREATE INDEX idx_tv_series_denormalized_rating ON tv_series_denormalized(our_rating DESC, view_count DESC);
CREATE INDEX idx_tv_series_denormalized_status ON tv_series_denormalized(series_status, release_year DESC);

-- =====================================================
-- REFRESH FUNCTIONS FOR MATERIALIZED VIEWS
-- =====================================================

-- Smart refresh function with change detection for movies
CREATE OR REPLACE FUNCTION refresh_movies_denormalized_smart()
RETURNS void AS $$
DECLARE
    last_refresh TIMESTAMP;
    changes_count INTEGER;
BEGIN
    -- Get last refresh time
    SELECT COALESCE(
        (SELECT obj_description(oid) FROM pg_class WHERE relname = 'movies_denormalized')::TIMESTAMP,
        '1970-01-01'::TIMESTAMP
    ) INTO last_refresh;

    -- Check for changes since last refresh
    SELECT COUNT(*) INTO changes_count
    FROM (
        SELECT 1 FROM movies WHERE updated_at > last_refresh
        UNION ALL
        SELECT 1 FROM movie_genres WHERE created_at > last_refresh
        UNION ALL
        SELECT 1 FROM movie_tags WHERE created_at > last_refresh
        UNION ALL
        SELECT 1 FROM movie_creators WHERE created_at > last_refresh
        UNION ALL
        SELECT 1 FROM movie_stream_sources WHERE updated_at > last_refresh
        UNION ALL
        SELECT 1 FROM content_tags WHERE updated_at > last_refresh
    ) changes;

    -- Only refresh if there are changes
    IF changes_count > 0 THEN
        REFRESH MATERIALIZED VIEW CONCURRENTLY movies_denormalized;

        -- Update last refresh timestamp
        COMMENT ON MATERIALIZED VIEW movies_denormalized IS NOW()::TEXT;

        RAISE NOTICE 'Refreshed movies_denormalized with % changes', changes_count;
    ELSE
        RAISE NOTICE 'No changes detected, skipping refresh';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Smart refresh function for TV series
CREATE OR REPLACE FUNCTION refresh_tv_series_denormalized_smart()
RETURNS void AS $$
DECLARE
    last_refresh TIMESTAMP;
    changes_count INTEGER;
BEGIN
    -- Get last refresh time
    SELECT COALESCE(
        (SELECT obj_description(oid) FROM pg_class WHERE relname = 'tv_series_denormalized')::TIMESTAMP,
        '1970-01-01'::TIMESTAMP
    ) INTO last_refresh;

    -- Check for changes since last refresh
    SELECT COUNT(*) INTO changes_count
    FROM (
        SELECT 1 FROM tv_series WHERE updated_at > last_refresh
        UNION ALL
        SELECT 1 FROM tv_series_genres WHERE created_at > last_refresh
        UNION ALL
        SELECT 1 FROM tv_series_tags WHERE created_at > last_refresh
        UNION ALL
        SELECT 1 FROM tv_series_creators WHERE created_at > last_refresh
        UNION ALL
        SELECT 1 FROM seasons WHERE updated_at > last_refresh
        UNION ALL
        SELECT 1 FROM content_tags WHERE updated_at > last_refresh
    ) changes;

    -- Only refresh if there are changes
    IF changes_count > 0 THEN
        REFRESH MATERIALIZED VIEW CONCURRENTLY tv_series_denormalized;

        -- Update last refresh timestamp
        COMMENT ON MATERIALIZED VIEW tv_series_denormalized IS NOW()::TEXT;

        RAISE NOTICE 'Refreshed tv_series_denormalized with % changes', changes_count;
    ELSE
        RAISE NOTICE 'No changes detected, skipping refresh';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- HIGH-PERFORMANCE QUERY FUNCTIONS
-- =====================================================

-- Multi-language search function with tags and keywords support
CREATE OR REPLACE FUNCTION search_movies(
    query_text TEXT,
    search_language TEXT DEFAULT 'english',
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0,
    tag_filter TEXT[] DEFAULT NULL
) RETURNS TABLE (
    id TEXT,
    title VARCHAR(500),
    synopsis TEXT,
    poster_url VARCHAR(1000),
    our_rating DECIMAL(3,1),
    view_count BIGINT,
    tags JSON,
    keywords TEXT[],
    rank REAL
) AS $$
DECLARE
    search_query TSQUERY;
    search_vector_column TEXT;
BEGIN
    -- Prepare search query with fuzzy matching
    search_query := plainto_tsquery(search_language, query_text);

    -- Select appropriate search vector
    search_vector_column := CASE
        WHEN search_language = 'english' THEN 'search_vector_en'
        ELSE 'search_vector_simple'
    END;

    -- Execute optimized search with tag filtering
    IF tag_filter IS NOT NULL AND array_length(tag_filter, 1) > 0 THEN
        RETURN QUERY EXECUTE format('
            SELECT m.id, m.title, m.synopsis, m.poster_url, m.our_rating, m.view_count,
                   md.tags, m.keywords,
                   ts_rank_cd(%I, $1) as rank
            FROM movies m
            JOIN movies_denormalized md ON m.id = md.id
            JOIN movie_tags mt ON m.id = mt.movie_id
            JOIN content_tags ct ON mt.tag_id = ct.id
            WHERE (%I @@ $1
               OR similarity(m.title_trigrams, lower($2)) > 0.3)
               AND ct.slug = ANY($5)
            GROUP BY m.id, m.title, m.synopsis, m.poster_url, m.our_rating, m.view_count,
                     md.tags, m.keywords
            ORDER BY
                ts_rank_cd(%I, $1) DESC,
                similarity(m.title_trigrams, lower($2)) DESC,
                m.view_count DESC
            LIMIT $3 OFFSET $4',
            search_vector_column, search_vector_column, search_vector_column
        ) USING search_query, query_text, limit_count, offset_count, tag_filter;
    ELSE
        RETURN QUERY EXECUTE format('
            SELECT m.id, m.title, m.synopsis, m.poster_url, m.our_rating, m.view_count,
                   md.tags, m.keywords,
                   ts_rank_cd(%I, $1) as rank
            FROM movies m
            JOIN movies_denormalized md ON m.id = md.id
            WHERE %I @@ $1
               OR similarity(m.title_trigrams, lower($2)) > 0.3
            ORDER BY
                ts_rank_cd(%I, $1) DESC,
                similarity(m.title_trigrams, lower($2)) DESC,
                m.view_count DESC
            LIMIT $3 OFFSET $4',
            search_vector_column, search_vector_column, search_vector_column
        ) USING search_query, query_text, limit_count, offset_count;
    END IF;
END;
$$ LANGUAGE plpgsql STABLE;

-- Get popular movies with caching support
CREATE OR REPLACE FUNCTION get_popular_movies(
    page_size INTEGER DEFAULT 20,
    page_offset INTEGER DEFAULT 0,
    genre_filter TEXT[] DEFAULT NULL,
    min_rating DECIMAL DEFAULT NULL
) RETURNS TABLE (
    id TEXT,
    title VARCHAR(500),
    poster_url VARCHAR(1000),
    our_rating DECIMAL(3,1),
    view_count BIGINT,
    genres JSON
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        md.id,
        md.title,
        md.poster_url,
        md.our_rating,
        md.view_count,
        md.genres
    FROM movies_denormalized md
    WHERE
        (genre_filter IS NULL OR md.genres::TEXT ~ ANY(genre_filter))
        AND (min_rating IS NULL OR md.our_rating >= min_rating)
        AND md.is_featured = TRUE
    ORDER BY md.view_count DESC, md.our_rating DESC
    LIMIT page_size OFFSET page_offset;
END;
$$ LANGUAGE plpgsql STABLE;

-- Get user watch history with continue watching
CREATE OR REPLACE FUNCTION get_user_watch_history(
    p_user_id TEXT,
    include_completed BOOLEAN DEFAULT FALSE,
    limit_count INTEGER DEFAULT 50
) RETURNS TABLE (
    movie_id TEXT,
    title VARCHAR(500),
    poster_url VARCHAR(1000),
    progress_percentage DECIMAL(5,2),
    last_watched_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        md.id,
        md.title,
        md.poster_url,
        uwh.progress_percentage,
        uwh.last_watched_at
    FROM user_movie_watch_history uwh
    JOIN movies_denormalized md ON uwh.movie_id = md.id
    WHERE
        uwh.user_id = p_user_id
        AND (include_completed OR NOT uwh.completed)
    ORDER BY uwh.last_watched_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql STABLE;

-- Get trending content
CREATE OR REPLACE FUNCTION get_trending_content(
    content_type VARCHAR(20) DEFAULT 'movie', -- 'movie' or 'tv_series'
    limit_count INTEGER DEFAULT 20
) RETURNS TABLE (
    id TEXT,
    title VARCHAR(500),
    poster_url VARCHAR(1000),
    our_rating DECIMAL(3,1),
    view_count BIGINT
) AS $$
BEGIN
    IF content_type = 'movie' THEN
        RETURN QUERY
        SELECT
            md.id,
            md.title,
            md.poster_url,
            md.our_rating,
            md.view_count
        FROM movies_denormalized md
        WHERE md.is_trending = TRUE
        ORDER BY md.view_count DESC, md.our_rating DESC
        LIMIT limit_count;
    ELSE
        RETURN QUERY
        SELECT
            tsd.id,
            tsd.title,
            tsd.poster_url,
            tsd.our_rating,
            tsd.view_count
        FROM tv_series_denormalized tsd
        WHERE tsd.is_trending = TRUE
        ORDER BY tsd.view_count DESC, tsd.our_rating DESC
        LIMIT limit_count;
    END IF;
END;
$$ LANGUAGE plpgsql STABLE;

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default content ratings
INSERT INTO content_ratings (name, description, min_age) VALUES
('G', 'General Audiences', 0),
('PG', 'Parental Guidance Suggested', 0),
('PG-13', 'Parents Strongly Cautioned', 13),
('R', 'Restricted', 17),
('NC-17', 'Adults Only', 18),
('TV-Y', 'All Children', 0),
('TV-Y7', 'Directed to Older Children', 7),
('TV-G', 'General Audience', 0),
('TV-PG', 'Parental Guidance Suggested', 0),
('TV-14', 'Parents Strongly Cautioned', 14),
('TV-MA', 'Mature Audience Only', 17);

-- Insert default languages
INSERT INTO languages (name, code, native_name, is_subtitle_available, is_audio_available) VALUES
('English', 'en', 'English', TRUE, TRUE),
('Vietnamese', 'vi', 'Tiếng Việt', TRUE, TRUE),
('Spanish', 'es', 'Español', TRUE, TRUE),
('French', 'fr', 'Français', TRUE, TRUE),
('German', 'de', 'Deutsch', TRUE, TRUE),
('Japanese', 'ja', '日本語', TRUE, TRUE),
('Korean', 'ko', '한국어', TRUE, TRUE),
('Chinese', 'zh', '中文', TRUE, TRUE),
('Portuguese', 'pt', 'Português', TRUE, TRUE),
('Italian', 'it', 'Italiano', TRUE, TRUE);

-- Insert default regions
INSERT INTO regions (name, code, region_type) VALUES
('United States', 'US', 'country'),
('Vietnam', 'VN', 'country'),
('United Kingdom', 'GB', 'country'),
('Canada', 'CA', 'country'),
('Australia', 'AU', 'country'),
('Germany', 'DE', 'country'),
('France', 'FR', 'country'),
('Japan', 'JP', 'country'),
('South Korea', 'KR', 'country'),
('China', 'CN', 'country');

-- Insert default genres
INSERT INTO genres (name, slug, description, sort_order) VALUES
('Action', 'action', 'High-energy films with physical stunts and chases', 1),
('Adventure', 'adventure', 'Exciting journeys and quests', 2),
('Comedy', 'comedy', 'Humorous content designed to entertain', 3),
('Drama', 'drama', 'Serious, plot-driven presentations', 4),
('Horror', 'horror', 'Designed to frighten and create suspense', 5),
('Romance', 'romance', 'Focus on love and romantic relationships', 6),
('Sci-Fi', 'sci-fi', 'Science fiction and futuristic themes', 7),
('Thriller', 'thriller', 'Suspenseful and exciting content', 8),
('Fantasy', 'fantasy', 'Magical and supernatural elements', 9),
('Crime', 'crime', 'Criminal activities and investigations', 10),
('Documentary', 'documentary', 'Non-fictional content', 11),
('Animation', 'animation', 'Animated content for all ages', 12),
('Family', 'family', 'Suitable for family viewing', 13),
('Music', 'music', 'Musical performances and stories', 14),
('War', 'war', 'Military conflicts and warfare', 15);

-- Insert default subscription plans
INSERT INTO subscriptions (name, description, price, currency, duration_days, features) VALUES
('Free', 'Limited access with ads', 0.00, 'USD', 30, ARRAY['Limited content', 'Ads included', 'SD quality']),
('Basic', 'Standard access with limited features', 9.99, 'USD', 30, ARRAY['Full content library', 'HD quality', 'No ads', '1 device']),
('Premium', 'Full access with all features', 19.99, 'USD', 30, ARRAY['Full content library', '4K quality', 'No ads', '4 devices', 'Download offline']),
('Family', 'Premium access for families', 29.99, 'USD', 30, ARRAY['Full content library', '4K quality', 'No ads', '6 devices', 'Download offline', 'Parental controls']);

-- Insert default content tags
INSERT INTO content_tags (name, slug, description, color_hex) VALUES
('Blockbuster', 'blockbuster', 'High-budget mainstream movies', '#FF6B6B'),
('Indie', 'indie', 'Independent films', '#4ECDC4'),
('Award Winner', 'award-winner', 'Award-winning content', '#FFD93D'),
('Trending', 'trending', 'Currently trending content', '#6BCF7F'),
('Classic', 'classic', 'Classic and timeless content', '#A8E6CF'),
('New Release', 'new-release', 'Recently released content', '#FF8B94'),
('Binge Watch', 'binge-watch', 'Perfect for binge watching', '#B4A7D6'),
('Feel Good', 'feel-good', 'Uplifting and positive content', '#FFAAA5'),
('Mind Bending', 'mind-bending', 'Complex and thought-provoking', '#FF677D'),
('Nostalgic', 'nostalgic', 'Brings back memories', '#D4A574'),
('Hidden Gem', 'hidden-gem', 'Underrated quality content', '#A8DADC'),
('Cult Following', 'cult-following', 'Has dedicated fanbase', '#F1FAEE'),
('Based on True Story', 'based-on-true-story', 'Based on real events', '#457B9D'),
('Superhero', 'superhero', 'Superhero themed content', '#1D3557'),
('Romantic', 'romantic', 'Romance focused content', '#E63946'),
('Dark', 'dark', 'Dark and intense themes', '#2D3748'),
('Funny', 'funny', 'Comedy and humorous content', '#F6E05E'),
('Emotional', 'emotional', 'Emotionally impactful content', '#ED8936'),
('Fast Paced', 'fast-paced', 'High energy and quick moving', '#38B2AC'),
('Slow Burn', 'slow-burn', 'Gradually building tension', '#805AD5');

-- =====================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================

COMMENT ON DATABASE vod_platform IS 'VOD Platform Production Database - Scalable from startup to millions of users';

COMMENT ON TABLE movies IS 'Core movies table with frequently accessed data and optimized search';
COMMENT ON TABLE tv_series IS 'Core TV series table with frequently accessed data and optimized search';
COMMENT ON TABLE users IS 'Core user accounts with authentication data';
COMMENT ON TABLE user_movie_watch_history IS 'User movie viewing history - hash partitioned by user_id for scalability';
COMMENT ON TABLE user_episode_watch_history IS 'User episode viewing history - hash partitioned by user_id for scalability';

COMMENT ON MATERIALIZED VIEW movies_denormalized IS 'Denormalized movie data for fast read access - refresh every 5 minutes';
COMMENT ON MATERIALIZED VIEW tv_series_denormalized IS 'Denormalized TV series data for fast read access - refresh every 5 minutes';

COMMENT ON FUNCTION generate_ulid() IS 'Generate ULID (Universally Unique Lexicographically Sortable Identifier) for better performance than UUID';
COMMENT ON FUNCTION search_movies(TEXT, TEXT, INTEGER, INTEGER) IS 'Multi-language movie search with fuzzy matching and autocomplete support';
COMMENT ON FUNCTION get_popular_movies(INTEGER, INTEGER, TEXT[], DECIMAL) IS 'Get popular movies with filtering and pagination';
COMMENT ON FUNCTION get_user_watch_history(TEXT, BOOLEAN, INTEGER) IS 'Get user watch history with continue watching support';

-- =====================================================
-- SCHEMA CREATION COMPLETE
-- =====================================================

-- Final message
DO $$
BEGIN
    RAISE NOTICE '==============================================';
    RAISE NOTICE 'VOD Platform Production Schema Created Successfully!';
    RAISE NOTICE '==============================================';
    RAISE NOTICE 'Features included:';
    RAISE NOTICE '- ULID primary keys for better performance';
    RAISE NOTICE '- Hash partitioning for user data scalability';
    RAISE NOTICE '- Multi-language search with autocomplete';
    RAISE NOTICE '- Materialized views for read scaling';
    RAISE NOTICE '- Comprehensive indexing strategy';
    RAISE NOTICE '- Production-ready with audit trails';
    RAISE NOTICE '- Optimized for millions of users';
    RAISE NOTICE '==============================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Setup PgBouncer for connection pooling';
    RAISE NOTICE '2. Configure automated materialized view refresh';
    RAISE NOTICE '3. Setup monitoring and alerting';
    RAISE NOTICE '4. Implement caching layer (Redis)';
    RAISE NOTICE '5. Setup read replicas for scaling';
    RAISE NOTICE '==============================================';
END $$;
